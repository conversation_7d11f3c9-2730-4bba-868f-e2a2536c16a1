import React, { useState, useEffect, useContext, useRef, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  Container,
  Button,
  Grid,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Radio,
  Checkbox,
  Chip,
  InputAdornment,
  List,
  ListItem,
  ListItemButton,
  Alert
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import PlaceIcon from '@mui/icons-material/Place';
import TableRestaurantIcon from '@mui/icons-material/TableRestaurant';
import EventSeatIcon from '@mui/icons-material/EventSeat';
import SaveIcon from '@mui/icons-material/Save';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import UndoIcon from '@mui/icons-material/Undo';
import RedoIcon from '@mui/icons-material/Redo';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import SearchIcon from '@mui/icons-material/Search';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import { EventContext } from '../../contexts/EventContext';
import { AuthContext } from '../../contexts/AuthContext';

// Floor Plan Preview Component
const FloorPlanPreview = ({ venue, guestList = [] }) => {
  const { t } = useTranslation();
  const canvasRef = useRef(null);

  useEffect(() => {
    if (!venue || !venue._id) {
      console.warn('Invalid venue object provided to FloorPlanPreview');
      return;
    }

    if (!venue?.floorPlan) {
      console.warn('No floor plan available for venue:', venue._id);
      return;
    }

    const canvas = canvasRef.current;
    if (!canvas) return;

    console.log('Rendering floor plan preview for venue:', venue._id);

    const ctx = canvas.getContext('2d');
    const { width, height, elements, background } = venue.floorPlan;

    // Set canvas dimensions
    canvas.width = width || 800;
    canvas.height = height || 600;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw background if available
    if (background) {
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        drawElements(ctx, elements, guestList);
      };
      img.src = background;
    } else {
      // Draw grid
      drawGrid(ctx, canvas.width, canvas.height);
      drawElements(ctx, elements, guestList);
    }
  }, [venue, guestList]);

  const drawGrid = (ctx, width, height) => {
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 0.5;

    // Draw vertical lines
    for (let x = 0; x <= width; x += 20) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }

    // Draw horizontal lines
    for (let y = 0; y <= height; y += 20) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
  };

  const drawElements = (ctx, elements, guestList) => {
    if (!elements) return;

    elements.forEach(element => {
      // Set colors based on element type and assignment status
      let fillColor = '#ffcc80'; // Default table color
      const hasAssignedGuests = element.assignedGuests && element.assignedGuests.length > 0;

      if (element.type === 'seat') {
        fillColor = hasAssignedGuests ? '#81d4fa' : '#81c784'; // Blue if assigned, green if not
      } else if (element.type === 'table' && hasAssignedGuests) {
        fillColor = '#b39ddb'; // Purple for tables with assigned guests
      }

      ctx.fillStyle = fillColor;
      ctx.strokeStyle = '#000';
      ctx.lineWidth = 1;

      if (element.type === 'table') {
        // Draw table
        ctx.fillRect(element.x, element.y, element.width, element.height);
        ctx.strokeRect(element.x, element.y, element.width, element.height);

        // Draw label
        ctx.fillStyle = '#000';
        ctx.font = '12px Arial';

        // Get assigned guests count if any
        const assignedCount = element.assignedGuests?.length || 0;
        const displayText = `${t('venue.floorPlanEditor.table', 'Table')} ${element.label || element.id}${assignedCount > 0 ? ` (${assignedCount})` : ''}`;

        ctx.fillText(
          displayText,
          element.x + element.width / 2 - (displayText.length * 3),
          element.y + element.height / 2 - 5
        );

        // If there are assigned guests, show their names
        if (assignedCount > 0) {
          const guestNames = element.assignedGuests
            .map(guestId => guestList.find(g => g._id === guestId)?.name || t('venue.floorPlanEditor.guest', 'Guest'))
            .join(', ');

          // Truncate if too long
          const truncatedNames = guestNames.length > 20 ? guestNames.substring(0, 18) + '...' : guestNames;

          ctx.font = '9px Arial';
          ctx.fillText(
            truncatedNames,
            element.x + element.width / 2 - (truncatedNames.length * 2),
            element.y + element.height / 2 + 10
          );
        }
      } else if (element.type === 'seat') {
        // Draw seat (circle)
        ctx.beginPath();
        ctx.arc(
          element.x + element.width / 2,
          element.y + element.height / 2,
          element.width / 2,
          0,
          2 * Math.PI
        );
        ctx.fill();
        ctx.stroke();

        // Draw label
        ctx.fillStyle = '#000';
        ctx.font = '10px Arial';

        // Show seat label or assigned guest name
        let displayText = element.label || element.id;

        // If there are assigned guests, show the first guest's name
        if (hasAssignedGuests) {
          const guestId = element.assignedGuests[0];
          const guestName = guestList.find(g => g._id === guestId)?.name;
          if (guestName) {
            displayText = guestName.substring(0, 10);
          } else {
            displayText = 'Assigned';
          }
        }

        ctx.fillText(
          displayText,
          element.x + element.width / 2 - (displayText.length * 2.5),
          element.y + element.height / 2 + 3
        );
      }
    });
  };

  return (
    <Box sx={{
      width: '100%',
      height: '100%',
      position: 'relative',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      overflow: 'hidden'
    }}>
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain',
          maxWidth: '100%',
          maxHeight: '100%'
        }}
      />
    </Box>
  );
};

// Guest Assignment Selector Component
const GuestAssignmentSelector = React.forwardRef(({ guests, onAssign, currentAssignment, isMultiSelect = false, currentAssignments = [] }, ref) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGuestId, setSelectedGuestId] = useState(currentAssignment || '');
  const [selectedGuestIds, setSelectedGuestIds] = useState(Array.isArray(currentAssignments) ? [...currentAssignments] : []);

  const filteredGuests = guests.filter(guest =>
    guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    guest.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAssign = () => {
    if (isMultiSelect) {
      onAssign(selectedGuestIds);
    } else {
      onAssign(selectedGuestId);
    }
  };

  const handleSelectGuest = (guestId) => {
    if (isMultiSelect) {
      // For multi-select, toggle the selection
      setSelectedGuestIds(prev => {
        if (prev.includes(guestId)) {
          return prev.filter(id => id !== guestId);
        } else {
          return [...prev, guestId];
        }
      });
    } else {
      // For single select, just set the ID
      setSelectedGuestId(guestId);
    }
  };

  const handleRemoveAll = () => {
    if (isMultiSelect) {
      setSelectedGuestIds([]);
      onAssign([]);
    } else {
      setSelectedGuestId('');
      onAssign(null);
    }
  };

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    handleAssign,
    getSelectedGuestId: () => selectedGuestId,
    getSelectedGuestIds: () => selectedGuestIds,
    clearSelection: () => {
      if (isMultiSelect) {
        setSelectedGuestIds([]);
      } else {
        setSelectedGuestId('');
      }
    }
  }));

  return (
    <Box>
      <TextField
        fullWidth
        variant="outlined"
        placeholder={t('venue.floorPlanEditor.searchGuests', 'Search guests...')}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        sx={{ mb: 2 }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
      />

      <TableContainer component={Paper} variant="outlined" sx={{ mb: 2, maxHeight: 300 }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox"></TableCell>
              <TableCell>{t('guests.columns.name', 'Name')}</TableCell>
              <TableCell>{t('guests.columns.email', 'Email')}</TableCell>
              <TableCell>{t('guests.columns.rsvpStatus', 'RSVP Status')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredGuests.length > 0 ? (
              filteredGuests.map((guest) => {
                const isSelected = isMultiSelect
                  ? selectedGuestIds.includes(guest._id)
                  : guest._id === selectedGuestId;

                return (
                  <TableRow
                    key={guest._id}
                    hover
                    selected={isSelected}
                    onClick={() => handleSelectGuest(guest._id)}
                  >
                    <TableCell padding="checkbox">
                      {isMultiSelect ? (
                        <Checkbox
                          checked={isSelected}
                          onChange={() => handleSelectGuest(guest._id)}
                        />
                      ) : (
                        <Radio
                          checked={isSelected}
                          onChange={() => handleSelectGuest(guest._id)}
                        />
                      )}
                    </TableCell>
                    <TableCell>{guest.name}</TableCell>
                    <TableCell>{guest.email}</TableCell>
                    <TableCell>
                      <Chip
                        label={guest.rsvpStatus}
                        color={
                          guest.rsvpStatus === 'Confirmed' ? 'success' :
                          guest.rsvpStatus === 'Pending' ? 'warning' : 'error'
                        }
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={4} align="center">
                  <Typography variant="body2" sx={{ py: 2 }}>
                    {t('guests.noGuests', 'No guests found')}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {isMultiSelect && selectedGuestIds.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            {t('venue.floorPlanEditor.guests', 'Guests')} ({selectedGuestIds.length}):
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {selectedGuestIds.map(guestId => {
              const guest = guests.find(g => g._id === guestId);
              return guest ? (
                <Chip
                  key={guestId}
                  label={guest.name}
                  onDelete={() => handleSelectGuest(guestId)}
                  color="primary"
                  variant="outlined"
                  size="small"
                />
              ) : null;
            })}
          </Box>
        </Box>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Button
          variant="outlined"
          color="error"
          onClick={handleRemoveAll}
          disabled={isMultiSelect ? selectedGuestIds.length === 0 : !currentAssignment}
        >
          {isMultiSelect ? t('venue.floorPlanEditor.removeAll', 'Remove All') : t('venue.floorPlanEditor.removeAssignment', 'Remove Assignment')}
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleAssign}
          disabled={isMultiSelect ? selectedGuestIds.length === 0 : !selectedGuestId}
        >
          {isMultiSelect ? t('venue.floorPlanEditor.assignGuests', 'Assign Guests') : t('venue.floorPlanEditor.assignGuest', 'Assign Guest')}
        </Button>
      </Box>
    </Box>
  );
});

// Canvas component for floor plan design
const FloorPlanDesigner = ({ venue, onSave, guestList = [], isMobile, isTablet, isLargeScreen = false, isUltraWide = false }) => {
  const { t } = useTranslation();
  const canvasRef = useRef(null);
  const fileInputRef = useRef(null);
  const canvasContainerRef = useRef(null);
  const guestSelectorRef = useRef(null);

  // Basic state
  const [elements, setElements] = useState(venue?.floorPlan?.elements || []);
  const [selectedElement, setSelectedElement] = useState(null);
  const [draggedElementType, setDraggedElementType] = useState(null);
  const [zoom, setZoom] = useState(1);
  const [backgroundImageUrl, setBackgroundImageUrl] = useState(venue?.floorPlan?.background || null);
  const [openGuestDialog, setOpenGuestDialog] = useState(false);
  // Dynamic canvas sizing based on screen size
  const getOptimalCanvasSize = () => {
    if (isUltraWide) return { width: 1200, height: 800 };
    if (isLargeScreen) return { width: 1000, height: 700 };
    if (isTablet) return { width: 600, height: 400 };
    if (isMobile) return { width: 400, height: 300 };
    return { width: 800, height: 600 }; // Default
  };

  const optimalSize = getOptimalCanvasSize();
  const [canvasWidth, setCanvasWidth] = useState(optimalSize.width);
  const [canvasHeight, setCanvasHeight] = useState(optimalSize.height);
  const [canvasReady, setCanvasReady] = useState(false);
  const [isTableAssignment, setIsTableAssignment] = useState(false);
  const [guestSearchTerm, setGuestSearchTerm] = useState('');
  const [draggedGuest, setDraggedGuest] = useState(null);
  const [isDroppingGuest, setIsDroppingGuest] = useState(false);
  const [dropTargetElement, setDropTargetElement] = useState(null);

  // Menu state
  const [anchorEl, setAnchorEl] = useState(null);

  // History state
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Dragging state
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [elementStart, setElementStart] = useState({ x: 0, y: 0 });

  // Resizing state
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState(null); // 'nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'
  const [elementStartDimensions, setElementStartDimensions] = useState({ width: 0, height: 0 });

  // Mouse position state for cursor styling
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Filtered guests based on search term
  const filteredGuests = guestList.filter(guest =>
    guest.name.toLowerCase().includes(guestSearchTerm.toLowerCase()) ||
    guest.email.toLowerCase().includes(guestSearchTerm.toLowerCase())
  );

  // Debug function to log the state of elements
  const debugElements = (message) => {
    console.log(`DEBUG [${message}]:`, {
      elementsCount: elements.length,
      tablesWithGuests: elements.filter(e => e.type === 'table' && e.assignedGuests && e.assignedGuests.length > 0).length,
      seatsWithGuests: elements.filter(e => e.type === 'seat' && e.assignedGuests && e.assignedGuests.length > 0).length,
      selectedElement: selectedElement ? {
        id: selectedElement.id,
        type: selectedElement.type,
        assignedGuests: selectedElement.assignedGuests || []
      } : null
    });
  };

  // Initialize elements from venue data if available
  useEffect(() => {
    if (!venue || !venue._id) {
      console.warn('Invalid venue object provided to FloorPlanDesigner');
      return;
    }

    console.log('Initializing floor plan designer with venue:', venue._id);

    if (venue?.floorPlan?.elements) {
      // Ensure all table elements have an assignedGuests array
      const processedElements = venue.floorPlan.elements.map(element => {
        if (element.type === 'table' && !element.assignedGuests) {
          return { ...element, assignedGuests: [] };
        }
        return element;
      });

      setElements(processedElements);
      // Save initial state to history
      setHistory([[...processedElements]]);
      setHistoryIndex(0);
    }
    if (venue?.floorPlan?.background) {
      setBackgroundImageUrl(venue.floorPlan.background);
    }
    if (venue?.floorPlan?.width && venue?.floorPlan?.height) {
      setCanvasWidth(venue.floorPlan.width);
      setCanvasHeight(venue.floorPlan.height);
    } else {
      // Use optimal size for new floor plans
      setCanvasWidth(optimalSize.width);
      setCanvasHeight(optimalSize.height);
    }

    // Set a small delay to ensure the canvas is ready after the dialog is fully open
    setTimeout(() => {
      setCanvasReady(true);
    }, 100);
  }, [venue]);

  // Redraw the canvas whenever elements, selection, or zoom changes
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !canvasReady) return;

    const ctx = canvas.getContext('2d');

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set scale based on zoom level
    ctx.save();
    ctx.scale(zoom, zoom);

    // Draw background image if available
    if (backgroundImageUrl) {
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, 0, 0, canvas.width / zoom, canvas.height / zoom);

        // Draw grid on top of background
        drawGrid(ctx, canvas.width / zoom, canvas.height / zoom);

        // Draw elements
        drawElements(ctx);

        ctx.restore();
      };
      img.src = backgroundImageUrl;
    } else {
      // Draw grid
      drawGrid(ctx, canvas.width / zoom, canvas.height / zoom);

      // Draw elements
      drawElements(ctx);

      ctx.restore();
    }
  }, [elements, selectedElement, zoom, backgroundImageUrl, canvasReady, isDragging]);

  // Force redraw when the component is mounted
  useEffect(() => {
    // Ensure the canvas is properly sized
    if (canvasRef.current) {
      canvasRef.current.width = canvasWidth;
      canvasRef.current.height = canvasHeight;

      // Force a redraw
      const ctx = canvasRef.current.getContext('2d');
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // Set a small delay to ensure the canvas is ready
      setTimeout(() => {
        setCanvasReady(true);
      }, 100);
    }

    // Add a window resize handler to ensure the canvas is redrawn when the window is resized
    const handleResize = () => {
      setCanvasReady(false);
      setTimeout(() => {
        setCanvasReady(true);
      }, 100);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [canvasWidth, canvasHeight]);

  const drawElements = (ctx) => {
    elements.forEach(element => {
      drawElement(ctx, element);

      // Highlight selected element
      if (selectedElement && element.id === selectedElement.id) {
        ctx.strokeStyle = '#2196f3';
        ctx.lineWidth = 2;
        ctx.strokeRect(
          element.x - 5,
          element.y - 5,
          element.width + 10,
          element.height + 10
        );

        // Draw resize handles for the selected element
        drawResizeHandles(ctx, element);
      }

      // Highlight drop target when dragging a guest
      if (isDroppingGuest && dropTargetElement && element.id === dropTargetElement.id) {
        ctx.strokeStyle = '#4caf50'; // Green highlight for drop target
        ctx.lineWidth = 3;
        ctx.setLineDash([5, 3]); // Dashed line
        ctx.strokeRect(
          element.x - 3,
          element.y - 3,
          element.width + 6,
          element.height + 6
        );
        ctx.setLineDash([]); // Reset to solid line
      }
    });
  };

  // Draw resize handles for the selected element
  const drawResizeHandles = (ctx, element) => {
    const handleSize = 8;
    ctx.fillStyle = '#2196f3';

    // Draw corner handles
    // Top-left
    ctx.fillRect(element.x - handleSize/2, element.y - handleSize/2, handleSize, handleSize);
    // Top-right
    ctx.fillRect(element.x + element.width - handleSize/2, element.y - handleSize/2, handleSize, handleSize);
    // Bottom-left
    ctx.fillRect(element.x - handleSize/2, element.y + element.height - handleSize/2, handleSize, handleSize);
    // Bottom-right
    ctx.fillRect(element.x + element.width - handleSize/2, element.y + element.height - handleSize/2, handleSize, handleSize);

    // Draw middle handles
    // Top
    ctx.fillRect(element.x + element.width/2 - handleSize/2, element.y - handleSize/2, handleSize, handleSize);
    // Bottom
    ctx.fillRect(element.x + element.width/2 - handleSize/2, element.y + element.height - handleSize/2, handleSize, handleSize);
    // Left
    ctx.fillRect(element.x - handleSize/2, element.y + element.height/2 - handleSize/2, handleSize, handleSize);
    // Right
    ctx.fillRect(element.x + element.width - handleSize/2, element.y + element.height/2 - handleSize/2, handleSize, handleSize);
  };

  const drawGrid = (ctx, width, height) => {
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 0.5;

    // Draw vertical lines
    for (let x = 0; x <= width; x += 20) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }

    // Draw horizontal lines
    for (let y = 0; y <= height; y += 20) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
  };

  const drawElement = (ctx, element) => {
    // Set colors based on element type and assignment status
    let fillColor = '#ffcc80'; // Default table color
    const hasAssignedGuests = element.assignedGuests && element.assignedGuests.length > 0;

    if (element.type === 'seat') {
      fillColor = hasAssignedGuests ? '#81d4fa' : '#81c784'; // Blue if assigned, green if not
    } else if (element.type === 'table' && hasAssignedGuests) {
      fillColor = '#b39ddb'; // Purple for tables with assigned guests
    }

    ctx.fillStyle = fillColor;
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 1;

    if (element.type === 'table') {
      // Draw table
      ctx.fillRect(element.x, element.y, element.width, element.height);
      ctx.strokeRect(element.x, element.y, element.width, element.height);

      // Draw label
      ctx.fillStyle = '#000';
      ctx.font = '12px Arial';

      // Get assigned guests count if any
      const assignedCount = element.assignedGuests?.length || 0;
      const displayText = `${t('venue.floorPlanEditor.table', 'Table')} ${element.label || element.id}${assignedCount > 0 ? ` (${assignedCount})` : ''}`;

      ctx.fillText(
        displayText,
        element.x + element.width / 2 - (displayText.length * 3),
        element.y + element.height / 2 - 5
      );

      // If there are assigned guests, show their names
      if (assignedCount > 0) {
        const guestNames = element.assignedGuests
          .map(guestId => guestList.find(g => g._id === guestId)?.name || t('venue.floorPlanEditor.guest', 'Guest'))
          .join(', ');

        // Truncate if too long
        const truncatedNames = guestNames.length > 20 ? guestNames.substring(0, 18) + '...' : guestNames;

        ctx.font = '9px Arial';
        ctx.fillText(
          truncatedNames,
          element.x + element.width / 2 - (truncatedNames.length * 2),
          element.y + element.height / 2 + 10
        );
      }
    } else if (element.type === 'seat') {
      // Draw seat (circle)
      ctx.beginPath();
      ctx.arc(
        element.x + element.width / 2,
        element.y + element.height / 2,
        element.width / 2,
        0,
        2 * Math.PI
      );
      ctx.fill();
      ctx.stroke();

      // Draw label
      ctx.fillStyle = '#000';
      ctx.font = '10px Arial';

      // Show seat label or assigned guest name
      let displayText = element.label || element.id;

      // If there are assigned guests, show the first guest's name
      if (hasAssignedGuests) {
        const guestId = element.assignedGuests[0];
        const guestName = guestList.find(g => g._id === guestId)?.name;
        if (guestName) {
          displayText = guestName.substring(0, 10);
        } else {
          displayText = t('venue.floorPlanEditor.assigned', 'Assigned');
        }
      }

      ctx.fillText(
        displayText,
        element.x + element.width / 2 - (displayText.length * 2.5),
        element.y + element.height / 2 + 3
      );
    }
  };

  // Handle dragging from element panel
  const handleElementDragStart = (e, type) => {
    e.dataTransfer.setData('text/plain', type);
    setDraggedElementType(type);
  };

  // Handle dropping element onto canvas
  const handleCanvasDrop = (e) => {
    e.preventDefault();

    // Check if we're dropping a guest
    const guestId = e.dataTransfer.getData('guest');
    if (guestId) {
      handleGuestDrop(e);
      return;
    }

    // Get type from dataTransfer or from state
    const elementType = e.dataTransfer.getData('text/plain') || draggedElementType;

    if (!elementType) {
      console.warn('No element type found during drop');
      return;
    }

    const canvas = canvasRef.current;
    const canvasRect = canvas.getBoundingClientRect();

    // Calculate the scaling factor between canvas display size and actual size
    const scaleX = canvas.width / canvasRect.width;
    const scaleY = canvas.height / canvasRect.height;

    // Get drop position in canvas coordinates
    const x = (e.clientX - canvasRect.left) * scaleX / zoom;
    const y = (e.clientY - canvasRect.top) * scaleY / zoom;

    console.log('Dropping element at:', x, y);

    // Create new element with the correct properties based on type
    let newElement;

    if (elementType === 'table') {
      // For tables
      newElement = {
        id: `${elementType}_${Date.now()}`, // Unique ID
        type: 'table',
        x: x,
        y: y,
        width: 80,
        height: 40,
        label: `T${elements.filter(e => e.type === 'table').length + 1}`,
        assignedGuests: [] // Empty array for tables
      };
    } else if (elementType === 'seat') {
      // For seats, also use assignedGuests array
      newElement = {
        id: `${elementType}_${Date.now()}`, // Unique ID
        type: 'seat',
        x: x,
        y: y,
        width: 20,
        height: 20,
        label: `S${elements.filter(e => e.type === 'seat').length + 1}`,
        assignedGuests: [] // Empty array for seats too
      };
    } else {
      // For other element types (if any)
      newElement = {
        id: `${elementType}_${Date.now()}`, // Unique ID
        type: elementType,
        x: x,
        y: y,
        width: 20,
        height: 20,
        label: `${elementType.charAt(0).toUpperCase()}${elements.filter(e => e.type === elementType).length + 1}`,
        assignedGuests: [] // Empty array for all elements
      };
    }

    console.log('Created new element:', JSON.stringify(newElement));

    // Save current state to history
    saveToHistory();

    // Add new element and select it
    setElements([...elements, newElement]);
    setSelectedElement(newElement);
    setDraggedElementType(null);

    console.log('Added new element:', newElement);
  };

  // Handle guest drag start
  const handleGuestDragStart = (e, guest) => {
    e.dataTransfer.setData('guest', guest._id);
    setDraggedGuest(guest);
    // Set a custom drag image if needed
    const dragImage = document.createElement('div');
    dragImage.textContent = guest.name;
    dragImage.style.backgroundColor = '#f0f0f0';
    dragImage.style.padding = '4px 8px';
    dragImage.style.borderRadius = '4px';
    dragImage.style.position = 'absolute';
    dragImage.style.top = '-1000px';
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 0, 0);
    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);
  };

  // Handle guest drop onto canvas
  const handleGuestDrop = (e) => {
    const guestId = e.dataTransfer.getData('guest');
    if (!guestId) return;

    const canvas = canvasRef.current;
    const canvasRect = canvas.getBoundingClientRect();

    // Calculate the scaling factor between canvas display size and actual size
    const scaleX = canvas.width / canvasRect.width;
    const scaleY = canvas.height / canvasRect.height;

    // Get drop position in canvas coordinates
    const x = (e.clientX - canvasRect.left) * scaleX / zoom;
    const y = (e.clientY - canvasRect.top) * scaleY / zoom;

    // Find element under the drop position
    const targetElement = findElementAtPosition(x, y);

    if (targetElement) {
      console.log(`Dropping guest ${guestId} onto element ${targetElement.id}`);

      // Create a deep copy of the elements array
      const updatedElements = elements.map(element => {
        if (element.id === targetElement.id) {
          // For seats, replace any existing assignment
          if (element.type === 'seat') {
            return {
              ...element,
              assignedGuests: [guestId] // Single guest for seats
            };
          }
          // For tables, add to the existing assignments if not already assigned
          else if (element.type === 'table') {
            const currentGuests = Array.isArray(element.assignedGuests) ? [...element.assignedGuests] : [];
            if (!currentGuests.includes(guestId)) {
              return {
                ...element,
                assignedGuests: [...currentGuests, guestId]
              };
            }
          }
        }
        return element;
      });

      // Update the elements state
      setElements(updatedElements);

      // If the target element is the selected element, update the selected element reference
      if (selectedElement && selectedElement.id === targetElement.id) {
        const updatedElement = updatedElements.find(e => e.id === targetElement.id);
        setSelectedElement(updatedElement);
      }

      // Save to history
      saveToHistory();
    }

    // Reset drag state
    setDraggedGuest(null);
    setIsDroppingGuest(false);
    setDropTargetElement(null);
  };

  const handleCanvasDragOver = (e) => {
    e.preventDefault(); // Allow dropping

    // Check if we're dragging a guest
    if (draggedGuest) {
      const canvas = canvasRef.current;
      const canvasRect = canvas.getBoundingClientRect();

      // Calculate the scaling factor between canvas display size and actual size
      const scaleX = canvas.width / canvasRect.width;
      const scaleY = canvas.height / canvasRect.height;

      // Get position in canvas coordinates
      const x = (e.clientX - canvasRect.left) * scaleX / zoom;
      const y = (e.clientY - canvasRect.top) * scaleY / zoom;

      // Find element under the cursor
      const targetElement = findElementAtPosition(x, y);

      // Update drop target state for visual feedback
      setIsDroppingGuest(true);
      setDropTargetElement(targetElement);
    }
  };

  // Find element under the cursor, optimized for different element types
  const findElementAtPosition = (x, y) => {
    // Search in reverse order to select top elements first
    for (let i = elements.length - 1; i >= 0; i--) {
      const element = elements[i];

      if (element.type === 'table') {
        // Rectangle hit detection
        if (
          x >= element.x &&
          x <= element.x + element.width &&
          y >= element.y &&
          y <= element.y + element.height
        ) {
          return element;
        }
      } else if (element.type === 'seat') {
        // Circle hit detection
        const centerX = element.x + element.width / 2;
        const centerY = element.y + element.height / 2;
        const radius = element.width / 2;
        const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));

        if (distance <= radius) {
          return element;
        }
      }
    }

    return null; // No element found
  };

  // Check if the cursor is over a resize handle of the selected element
  const getResizeHandleAtPosition = (x, y) => {
    if (!selectedElement) return null;

    const handleSize = 8;
    const element = selectedElement;

    // Check corner handles
    // Top-left (nw)
    if (
      x >= element.x - handleSize/2 && x <= element.x + handleSize/2 &&
      y >= element.y - handleSize/2 && y <= element.y + handleSize/2
    ) {
      return 'nw';
    }

    // Top-right (ne)
    if (
      x >= element.x + element.width - handleSize/2 && x <= element.x + element.width + handleSize/2 &&
      y >= element.y - handleSize/2 && y <= element.y + handleSize/2
    ) {
      return 'ne';
    }

    // Bottom-left (sw)
    if (
      x >= element.x - handleSize/2 && x <= element.x + handleSize/2 &&
      y >= element.y + element.height - handleSize/2 && y <= element.y + element.height + handleSize/2
    ) {
      return 'sw';
    }

    // Bottom-right (se)
    if (
      x >= element.x + element.width - handleSize/2 && x <= element.x + element.width + handleSize/2 &&
      y >= element.y + element.height - handleSize/2 && y <= element.y + element.height + handleSize/2
    ) {
      return 'se';
    }

    // Check middle handles
    // Top (n)
    if (
      x >= element.x + element.width/2 - handleSize/2 && x <= element.x + element.width/2 + handleSize/2 &&
      y >= element.y - handleSize/2 && y <= element.y + handleSize/2
    ) {
      return 'n';
    }

    // Bottom (s)
    if (
      x >= element.x + element.width/2 - handleSize/2 && x <= element.x + element.width/2 + handleSize/2 &&
      y >= element.y + element.height - handleSize/2 && y <= element.y + element.height + handleSize/2
    ) {
      return 's';
    }

    // Left (w)
    if (
      x >= element.x - handleSize/2 && x <= element.x + handleSize/2 &&
      y >= element.y + element.height/2 - handleSize/2 && y <= element.y + element.height/2 + handleSize/2
    ) {
      return 'w';
    }

    // Right (e)
    if (
      x >= element.x + element.width - handleSize/2 && x <= element.x + element.width + handleSize/2 &&
      y >= element.y + element.height/2 - handleSize/2 && y <= element.y + element.height/2 + handleSize/2
    ) {
      return 'e';
    }

    return null;
  };

  // Get cursor style based on resize handle
  const getResizeCursor = (handle) => {
    switch (handle) {
      case 'nw': return 'nwse-resize';
      case 'ne': return 'nesw-resize';
      case 'sw': return 'nesw-resize';
      case 'se': return 'nwse-resize';
      case 'n': return 'ns-resize';
      case 's': return 'ns-resize';
      case 'e': return 'ew-resize';
      case 'w': return 'ew-resize';
      default: return selectedElement ? 'grab' : 'default';
    }
  };

  // Save current state to history
  const saveToHistory = () => {
    // Create a deep copy of the elements array to ensure all nested objects are copied
    const elementsCopy = elements.map(element => {
      // For tables, make sure to create a new assignedGuests array
      if (element.type === 'table') {
        return {
          ...element,
          assignedGuests: Array.isArray(element.assignedGuests) ? [...element.assignedGuests] : []
        };
      }
      return { ...element };
    });

    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(elementsCopy);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);

    console.log('Saved to history, elements count:', elementsCopy.length);
  };

  // Handle mouse down - start dragging, resizing, or select
  const handleMouseDown = (e) => {
    if (!canvasRef.current) return;

    // Get canvas dimensions
    const canvas = canvasRef.current;
    const canvasRect = canvas.getBoundingClientRect();

    // Calculate the scaling factor between canvas display size and actual size
    const scaleX = canvas.width / canvasRect.width;
    const scaleY = canvas.height / canvasRect.height;

    // Get mouse position in canvas coordinates
    const mouseX = (e.clientX - canvasRect.left) * scaleX / zoom;
    const mouseY = (e.clientY - canvasRect.top) * scaleY / zoom;

    // Update mouse position for cursor styling
    setMousePosition({ x: mouseX, y: mouseY });

    console.log('Mouse down at:', mouseX, mouseY);

    // Check if we're clicking on a resize handle of the selected element
    if (selectedElement) {
      const handle = getResizeHandleAtPosition(mouseX, mouseY);
      if (handle) {
        // Start resizing
        setIsResizing(true);
        setResizeHandle(handle);
        setDragStart({x: mouseX, y: mouseY});
        setElementStartDimensions({
          width: selectedElement.width,
          height: selectedElement.height
        });
        setElementStart({
          x: selectedElement.x,
          y: selectedElement.y
        });
        return;
      }
    }

    // Find element under cursor
    const clickedElement = findElementAtPosition(mouseX, mouseY);

    if (clickedElement) {
      // Store drag start positions
      setDragStart({x: mouseX, y: mouseY});
      setElementStart({x: clickedElement.x, y: clickedElement.y});

      // Select the element
      setSelectedElement(clickedElement);
      setIsDragging(true);

      console.log('Selected element:', clickedElement.id, 'at position:', clickedElement.x, clickedElement.y);
    } else {
      // Clicked empty space, deselect
      setSelectedElement(null);
      setIsDragging(false);
    }
  };

  // Handle mouse move - drag or resize the element
  const handleMouseMove = (e) => {
    if (!canvasRef.current) return;

    // Get canvas dimensions
    const canvas = canvasRef.current;
    const canvasRect = canvas.getBoundingClientRect();

    // Calculate the scaling factor between canvas display size and actual size
    const scaleX = canvas.width / canvasRect.width;
    const scaleY = canvas.height / canvasRect.height;

    // Get mouse position in canvas coordinates
    const mouseX = (e.clientX - canvasRect.left) * scaleX / zoom;
    const mouseY = (e.clientY - canvasRect.top) * scaleY / zoom;

    // Update mouse position for cursor styling
    setMousePosition({ x: mouseX, y: mouseY });

    if ((!isDragging && !isResizing) || !selectedElement) return;

    if (isResizing) {
      // Calculate movement delta
      const deltaX = mouseX - dragStart.x;
      const deltaY = mouseY - dragStart.y;

      // Minimum dimensions for elements
      const minWidth = 20;
      const minHeight = 20;

      // Calculate new dimensions based on which handle is being dragged
      let newX = elementStart.x;
      let newY = elementStart.y;
      let newWidth = elementStartDimensions.width;
      let newHeight = elementStartDimensions.height;

      switch (resizeHandle) {
        case 'nw': // Top-left
          newX = elementStart.x + deltaX;
          newY = elementStart.y + deltaY;
          newWidth = Math.max(elementStartDimensions.width - deltaX, minWidth);
          newHeight = Math.max(elementStartDimensions.height - deltaY, minHeight);
          break;
        case 'ne': // Top-right
          newY = elementStart.y + deltaY;
          newWidth = Math.max(elementStartDimensions.width + deltaX, minWidth);
          newHeight = Math.max(elementStartDimensions.height - deltaY, minHeight);
          break;
        case 'sw': // Bottom-left
          newX = elementStart.x + deltaX;
          newWidth = Math.max(elementStartDimensions.width - deltaX, minWidth);
          newHeight = Math.max(elementStartDimensions.height + deltaY, minHeight);
          break;
        case 'se': // Bottom-right
          newWidth = Math.max(elementStartDimensions.width + deltaX, minWidth);
          newHeight = Math.max(elementStartDimensions.height + deltaY, minHeight);
          break;
        case 'n': // Top
          newY = elementStart.y + deltaY;
          newHeight = Math.max(elementStartDimensions.height - deltaY, minHeight);
          break;
        case 's': // Bottom
          newHeight = Math.max(elementStartDimensions.height + deltaY, minHeight);
          break;
        case 'w': // Left
          newX = elementStart.x + deltaX;
          newWidth = Math.max(elementStartDimensions.width - deltaX, minWidth);
          break;
        case 'e': // Right
          newWidth = Math.max(elementStartDimensions.width + deltaX, minWidth);
          break;
        default:
          break;
      }

      // Update elements array with new dimensions
      const updatedElements = elements.map(element => {
        if (element.id === selectedElement.id) {
          return { ...element, x: newX, y: newY, width: newWidth, height: newHeight };
        }
        return element;
      });

      // Update state
      setElements(updatedElements);

      // Also update selected element reference to keep UI in sync
      setSelectedElement({...selectedElement, x: newX, y: newY, width: newWidth, height: newHeight});
    } else if (isDragging) {
      // Calculate movement delta
      const deltaX = mouseX - dragStart.x;
      const deltaY = mouseY - dragStart.y;

      // Apply movement to element
      const newX = elementStart.x + deltaX;
      const newY = elementStart.y + deltaY;

      // Update elements array with new position
      const updatedElements = elements.map(element => {
        if (element.id === selectedElement.id) {
          return { ...element, x: newX, y: newY };
        }
        return element;
      });

      // Update state
      setElements(updatedElements);

      // Also update selected element reference to keep UI in sync
      setSelectedElement({...selectedElement, x: newX, y: newY});
    }
  };

  // Handle mouse up - finish dragging or resizing
  const handleMouseUp = () => {
    if ((isDragging || isResizing) && selectedElement) {
      // Save the state to history
      saveToHistory();
      console.log('Finished ' + (isDragging ? 'dragging' : 'resizing') + ' element:', selectedElement.id);
    }

    setIsDragging(false);
    setIsResizing(false);
    setResizeHandle(null);
  };

  // Handle mouse leave - cancel dragging or resizing if cursor leaves canvas
  const handleMouseLeave = () => {
    if (isDragging || isResizing) {
      // Save current state if we were dragging or resizing
      saveToHistory();
    }

    setIsDragging(false);
    setIsResizing(false);
    setResizeHandle(null);
  };

  const handleZoomIn = () => {
    setZoom(Math.min(zoom + 0.1, 2));
  };

  const handleZoomOut = () => {
    setZoom(Math.max(zoom - 0.1, 0.5));
  };

  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setElements([...history[historyIndex - 1]]);
      // Deselect when undoing
      setSelectedElement(null);
    }
  };

  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setElements([...history[historyIndex + 1]]);
      // Deselect when redoing
      setSelectedElement(null);
    }
  };

  const handleSave = () => {
    // Make sure we have valid data before saving
    if (elements && Array.isArray(elements)) {
      console.log('Saving floor plan with', elements.length, 'elements');
      onSave(elements, backgroundImageUrl, canvasWidth, canvasHeight);
    } else {
      console.error('Cannot save floor plan: invalid elements', elements);
    }
  };

  const handleUploadImage = (event) => {
    const file = event.target.files[0];
    if (file) {
      // For immediate preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setBackgroundImageUrl(e.target.result);
      };
      reader.readAsDataURL(file);

      // If we have a venue ID, we can upload the image to the server
      if (venue?._id) {
        uploadImageToServer(file, venue._id);
      }
    }
  };

  const uploadImageToServer = async (file, venueId) => {
    if (!venueId) {
      console.error('Invalid venue ID for image upload');
      return;
    }

    const formData = new FormData();
    formData.append('image', file);

    try {
      // Get auth token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      console.log('Uploading image for venue ID:', venueId);

      const response = await fetch(`/api/resources/venues/${venueId}/floorplan/background`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to upload background image');
      }

      const data = await response.json();
      console.log('Background image uploaded successfully:', data);
      // We don't need to update the backgroundImageUrl here as we're already showing the preview
    } catch (error) {
      console.error('Error uploading background image:', error);
      alert(t('venue.errors.uploadBackgroundFailed', 'Error uploading background image. The floor plan will still work, but the background may not be saved permanently.'));
    }
  };

  const handleOpenGuestDialog = () => {
    if (selectedElement) {
      if (selectedElement.type === 'seat') {
        setIsTableAssignment(false);
        setOpenGuestDialog(true);
      } else if (selectedElement.type === 'table') {
        setIsTableAssignment(true);
        setOpenGuestDialog(true);
      }
    }
  };

  const handleCloseGuestDialog = () => {
    setOpenGuestDialog(false);
  };

  const handleAssignGuest = (guestId) => {
    if (!selectedElement) return;

    debugElements('Before assignment');

    // For both seats and tables, use assignedGuests
    // For seats, we'll use a single-item array
    // For tables, we'll use the full array

    // Make sure guestId is properly formatted
    let guestIds;

    if (selectedElement.type === 'seat') {
      // For seats, we use a single-item array (or empty array if null)
      guestIds = guestId ? [guestId] : [];
      console.log('Assigning guest to seat:', JSON.stringify(guestIds));
    } else {
      // For tables, we use the full array
      guestIds = Array.isArray(guestId) ? [...guestId] : [];
      console.log('Assigning guests to table:', JSON.stringify(guestIds));
    }

    // Create a deep copy of the elements array
    const updatedElements = elements.map(element => {
      if (element.id === selectedElement.id) {
        // Create a new object with the updated assignedGuests array
        return {
          ...element,
          assignedGuests: [...guestIds] // Make sure to create a new array
        };
      }
      return element;
    });

    // Update the elements state with the new array
    setElements(updatedElements);

    // Create a new object for the selected element with the updated assignedGuests array
    const updatedSelectedElement = {
      ...selectedElement,
      assignedGuests: [...guestIds] // Make sure to create a new array
    };

    // Update the selected element reference
    setSelectedElement(updatedSelectedElement);

    console.log('Updated element:', JSON.stringify(updatedSelectedElement));

    // Save to history
    saveToHistory();

    debugElements('After assignment');

    handleCloseGuestDialog();
  };

  const handleRemoveAssignment = () => {
    if (!selectedElement) return;

    debugElements('Before removal');

    // For both seats and tables, clear the assignedGuests array
    console.log('Removing all guest assignments from element:', selectedElement.id);

    // Create a deep copy of the elements array
    const updatedElements = elements.map(element => {
      if (element.id === selectedElement.id) {
        // Create a new object with an empty assignedGuests array
        return {
          ...element,
          assignedGuests: [] // Empty array, not null or undefined
        };
      }
      return element;
    });

    // Update the elements state with the new array
    setElements(updatedElements);

    // Create a new object for the selected element with an empty assignedGuests array
    const updatedSelectedElement = {
      ...selectedElement,
      assignedGuests: [] // Empty array, not null or undefined
    };

    // Update the selected element reference
    setSelectedElement(updatedSelectedElement);

    console.log('Updated element after removing guests:', JSON.stringify(updatedSelectedElement));

    // Save to history
    saveToHistory();

    debugElements('After removal');

    handleMenuClose();
  };

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDeleteElement = () => {
    if (selectedElement) {
      // Save current state to history
      saveToHistory();

      // Remove the element
      setElements(elements.filter(element => element.id !== selectedElement.id));
      setSelectedElement(null);
      handleMenuClose();
    }
  };

  // Move the selected element to the front (end of the array)
  const handleMoveToFront = () => {
    if (selectedElement) {
      // Save current state to history
      saveToHistory();

      // Remove the element from its current position and add it to the end
      const updatedElements = elements.filter(element => element.id !== selectedElement.id);
      updatedElements.push(selectedElement);

      setElements(updatedElements);
      handleMenuClose();
    }
  };

  // Move the selected element to the back (beginning of the array)
  const handleMoveToBack = () => {
    if (selectedElement) {
      // Save current state to history
      saveToHistory();

      // Remove the element from its current position and add it to the beginning
      const updatedElements = elements.filter(element => element.id !== selectedElement.id);
      updatedElements.unshift(selectedElement);

      setElements(updatedElements);
      handleMenuClose();
    }
  };

  // Add state for touch support
  const [isTouchDragging, setIsTouchDragging] = useState(false);
  const [touchStart, setTouchStart] = useState({ x: 0, y: 0 });
  const [touchElementStart, setTouchElementStart] = useState({ x: 0, y: 0 });
  const [touchResizeHandle, setTouchResizeHandle] = useState(null);
  const [pendingAddType, setPendingAddType] = useState(null); // For tap-to-add on mobile
  const [pendingGuest, setPendingGuest] = useState(null); // For tap-to-assign guest on mobile

  // --- TOUCH EVENT HELPERS ---
  // Convert touch event to canvas coordinates
  const getTouchPos = (touch) => {
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    return {
      x: (touch.clientX - rect.left) * scaleX / zoom,
      y: (touch.clientY - rect.top) * scaleY / zoom,
    };
  };

  // Touch start handler
  const handleTouchStart = (e) => {
    if (!canvasRef.current) return;
    if (e.touches.length !== 1) return; // Only single touch for now
    const touch = e.touches[0];
    const { x, y } = getTouchPos(touch);
    setMousePosition({ x, y });

    // If pending add (from tap on element panel), add new element here
    if (pendingAddType) {
      handleAddElementAt(x, y, pendingAddType);
      setPendingAddType(null);
      e.preventDefault();
      return;
    }
    // If pending guest assignment, assign guest to tapped element
    if (pendingGuest) {
      const target = findElementAtPosition(x, y);
      if (target) {
        handleAssignGuestToElement(target, pendingGuest);
        setPendingGuest(null);
        e.preventDefault();
        return;
      }
    }
    // Check for resize handle
    if (selectedElement) {
      const handle = getResizeHandleAtPosition(x, y);
      if (handle) {
        setIsResizing(true);
        setTouchResizeHandle(handle);
        setTouchStart({ x, y });
        setElementStartDimensions({ width: selectedElement.width, height: selectedElement.height });
        setElementStart({ x: selectedElement.x, y: selectedElement.y });
        setIsTouchDragging(true);
        e.preventDefault();
        return;
      }
    }
    // Check for element under touch
    const touchedElement = findElementAtPosition(x, y);
    if (touchedElement) {
      setSelectedElement(touchedElement);
      setIsDragging(true);
      setIsTouchDragging(true);
      setTouchStart({ x, y });
      setTouchElementStart({ x: touchedElement.x, y: touchedElement.y });
      e.preventDefault();
    } else {
      setSelectedElement(null);
      setIsDragging(false);
      setIsTouchDragging(false);
    }
  };

  // Touch move handler
  const handleTouchMove = (e) => {
    if (!isTouchDragging || !canvasRef.current) return;
    if (e.touches.length !== 1) return;
    const touch = e.touches[0];
    const { x, y } = getTouchPos(touch);
    setMousePosition({ x, y });
    if (isResizing && selectedElement) {
      // Same as mouse resize logic
      const deltaX = x - touchStart.x;
      const deltaY = y - touchStart.y;
      let newX = elementStart.x;
      let newY = elementStart.y;
      let newWidth = elementStartDimensions.width;
      let newHeight = elementStartDimensions.height;
      const minWidth = 20, minHeight = 20;
      switch (touchResizeHandle) {
        case 'nw': newX += deltaX; newY += deltaY; newWidth = Math.max(newWidth - deltaX, minWidth); newHeight = Math.max(newHeight - deltaY, minHeight); break;
        case 'ne': newY += deltaY; newWidth = Math.max(newWidth + deltaX, minWidth); newHeight = Math.max(newHeight - deltaY, minHeight); break;
        case 'sw': newX += deltaX; newWidth = Math.max(newWidth - deltaX, minWidth); newHeight = Math.max(newHeight + deltaY, minHeight); break;
        case 'se': newWidth = Math.max(newWidth + deltaX, minWidth); newHeight = Math.max(newHeight + deltaY, minHeight); break;
        case 'n': newY += deltaY; newHeight = Math.max(newHeight - deltaY, minHeight); break;
        case 's': newHeight = Math.max(newHeight + deltaY, minHeight); break;
        case 'w': newX += deltaX; newWidth = Math.max(newWidth - deltaX, minWidth); break;
        case 'e': newWidth = Math.max(newWidth + deltaX, minWidth); break;
        default: break;
      }
      const updatedElements = elements.map(el => el.id === selectedElement.id ? { ...el, x: newX, y: newY, width: newWidth, height: newHeight } : el);
      setElements(updatedElements);
      setSelectedElement({ ...selectedElement, x: newX, y: newY, width: newWidth, height: newHeight });
      e.preventDefault();
    } else if (isDragging && selectedElement) {
      const deltaX = x - touchStart.x;
      const deltaY = y - touchStart.y;
      const newX = touchElementStart.x + deltaX;
      const newY = touchElementStart.y + deltaY;
      const updatedElements = elements.map(el => el.id === selectedElement.id ? { ...el, x: newX, y: newY } : el);
      setElements(updatedElements);
      setSelectedElement({ ...selectedElement, x: newX, y: newY });
      e.preventDefault();
    }
  };

  // Touch end handler
  const handleTouchEnd = (e) => {
    if (isTouchDragging && selectedElement) {
      saveToHistory();
    }
    setIsDragging(false);
    setIsResizing(false);
    setIsTouchDragging(false);
    setTouchResizeHandle(null);
  };

  // Helper: Add element at position (for tap-to-add)
  const handleAddElementAt = (x, y, type) => {
    let newElement;
    if (type === 'table') {
      newElement = { id: `table_${Date.now()}`, type: 'table', x, y, width: 80, height: 40, label: `T${elements.filter(e => e.type === 'table').length + 1}`, assignedGuests: [] };
    } else if (type === 'seat') {
      newElement = { id: `seat_${Date.now()}`, type: 'seat', x, y, width: 20, height: 20, label: `S${elements.filter(e => e.type === 'seat').length + 1}`, assignedGuests: [] };
    }
    saveToHistory();
    setElements([...elements, newElement]);
    setSelectedElement(newElement);
  };

  // Helper: Assign guest to element (for tap-to-assign)
  const handleAssignGuestToElement = (element, guest) => {
    const updatedElements = elements.map(el => {
      if (el.id === element.id) {
        if (el.type === 'seat') return { ...el, assignedGuests: [guest._id] };
        if (el.type === 'table') {
          const current = Array.isArray(el.assignedGuests) ? [...el.assignedGuests] : [];
          if (!current.includes(guest._id)) return { ...el, assignedGuests: [...current, guest._id] };
        }
      }
      return el;
    });
    setElements(updatedElements);
    setSelectedElement({ ...element, assignedGuests: [guest._id] });
    saveToHistory();
  };

  // --- END TOUCH EVENT HELPERS ---

  // --- MOBILE/TABLET FRIENDLY ELEMENT PANEL ---
  // On mobile, tap an element type to enable tap-to-place mode
  // On tablet, support both tap and drag
  // Using isMobile and isTablet props passed from parent component
  const isTouchDevice = isMobile || isTablet;

  return (
    <Box sx={{ width: '100%', height: '100%', position: 'relative' }}>
      <Box sx={{ mb: 2, display: 'flex', flexDirection: { xs: 'column', md: 'row' }, justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: { xs: 1, md: 0 } }}>
          <Button
            variant="outlined"
            component="label"
            size="small"
            startIcon={<AddIcon />}
          >
            {t('venue.floorPlanEditor.uploadImage', 'Upload Image')}
            <input
              ref={fileInputRef}
              type="file"
              hidden
              accept="image/*"
              onChange={handleUploadImage}
            />
          </Button>
        </Box>
        <Box>
          <Tooltip title="Zoom In">
            <IconButton onClick={handleZoomIn} size="small" sx={{ mr: 1 }}>
              <ZoomInIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom Out">
            <IconButton onClick={handleZoomOut} size="small" sx={{ mr: 1 }}>
              <ZoomOutIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Undo">
            <span>
              <IconButton
                onClick={handleUndo}
                size="small"
                sx={{ mr: 1 }}
                disabled={historyIndex <= 0}
              >
                <UndoIcon />
              </IconButton>
            </span>
          </Tooltip>
          <Tooltip title="Redo">
            <span>
              <IconButton
                onClick={handleRedo}
                size="small"
                sx={{ mr: 1 }}
                disabled={historyIndex >= history.length - 1}
              >
                <RedoIcon />
              </IconButton>
            </span>
          </Tooltip>
          <Tooltip title="More Actions">
            <span>
              <IconButton
                onClick={handleMenuOpen}
                size="small"
                disabled={!selectedElement}
              >
                <MoreVertIcon />
              </IconButton>
            </span>
          </Tooltip>
        </Box>
      </Box>
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: isTablet ? 'row' : 'column', md: 'row' },
        height: { xs: 'auto', sm: isTablet ? 'calc(90vh - 200px)' : 500, md: 600 },
        gap: 2,
        overflow: 'hidden',
        flex: 1
      }}>
        {/* Element Panel */}
        <Paper
          variant="outlined"
          sx={{
            width: {
              xs: '100%',
              sm: isTablet ? 100 : 120,
              md: 140,
              lg: 160,
              xl: 180
            },
            height: { sm: isTablet ? '100%' : 'auto' },
            mb: { xs: 1, sm: 0 },
            mr: { sm: 1, md: 2 },
            p: { xs: 1, sm: isTablet ? 1 : 1.5, md: 2 },
            display: 'flex',
            flexDirection: { xs: 'row', sm: 'column' },
            alignItems: 'center',
            gap: { xs: 1, sm: isTablet ? 1 : 1.5, md: 2 },
            justifyContent: { xs: 'center', sm: 'flex-start' },
            overflow: isTablet ? 'auto' : 'visible',
            flexShrink: 0,
          }}
        >
          <Typography variant="subtitle2" gutterBottom align="center" sx={{ display: { xs: 'none', sm: 'block' } }}>
            {t('venue.floorPlanEditor.dragElements', 'Drag Elements')}
          </Typography>
          {/* Table Element */}
          <Paper
            sx={{
              p: 1,
              width: { xs: 60, sm: '100%' },
              height: { xs: 60, sm: 'auto' },
              cursor: isTouchDevice ? 'pointer' : 'grab',
              bgcolor: '#ffcc80',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              border: '1px solid #ccc',
              justifyContent: 'center',
              fontSize: 24,
            }}
            draggable={!isTouchDevice}
            onDragStart={isTouchDevice ? undefined : (e) => handleElementDragStart(e, 'table')}
            onClick={isTouchDevice ? () => setPendingAddType('table') : undefined}
            elevation={pendingAddType === 'table' ? 6 : 1}
          >
            <TableRestaurantIcon fontSize="large" />
            <Typography variant="caption">{t('venue.floorPlanEditor.table', 'Table')}</Typography>
            {isTouchDevice && pendingAddType === 'table' && <Chip label={t('venue.floorPlanEditor.tapToPlace', 'Tap to place')} color="primary" size="small" sx={{ mt: 1 }} />}
          </Paper>
          {/* Seat Element */}
          <Paper
            sx={{
              p: 1,
              width: { xs: 60, sm: '100%' },
              height: { xs: 60, sm: 'auto' },
              cursor: isTouchDevice ? 'pointer' : 'grab',
              bgcolor: '#81c784',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              border: '1px solid #ccc',
              justifyContent: 'center',
              fontSize: 24,
            }}
            draggable={!isTouchDevice}
            onDragStart={isTouchDevice ? undefined : (e) => handleElementDragStart(e, 'seat')}
            onClick={isTouchDevice ? () => setPendingAddType('seat') : undefined}
            elevation={pendingAddType === 'seat' ? 6 : 1}
          >
            <EventSeatIcon fontSize="large" />
            <Typography variant="caption">{t('venue.floorPlanEditor.seat', 'Seat')}</Typography>
            {isTouchDevice && pendingAddType === 'seat' && <Chip label={t('venue.floorPlanEditor.tapToPlace', 'Tap to place')} color="primary" size="small" sx={{ mt: 1 }} />}
          </Paper>
          <Typography variant="caption" align="center" sx={{ mt: 2, display: { xs: 'none', sm: 'block' } }}>
            {isTablet
              ? t('venue.floorPlanEditor.dragOrTapElements', 'Drag or tap elements onto the canvas')
              : t('venue.floorPlanEditor.dragAndDropElements', 'Drag and drop elements onto the canvas')}
          </Typography>
        </Paper>
        {/* Canvas */}
        <Paper
          variant="outlined"
          sx={{
            flexGrow: 1,
            height: { xs: 300, sm: '100%', md: '100%' },
            minHeight: { xs: 300, sm: 400, md: 500 },
            overflow: 'auto',
            position: 'relative',
            touchAction: isTablet ? 'pan-x pan-y' : 'none', // Allow panning on tablets but maintain touch control
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            mb: { xs: 1, sm: 0 },
            border: isTablet ? '2px solid #e0e0e0' : '1px solid #e0e0e0', // More visible border on tablet
          }}
          ref={canvasContainerRef}
          onDrop={isTouchDevice ? undefined : handleCanvasDrop}
          onDragOver={isTouchDevice ? undefined : handleCanvasDragOver}
        >
          <canvas
            ref={canvasRef}
            width={canvasWidth}
            height={canvasHeight}
            onMouseDown={isTouchDevice ? undefined : handleMouseDown}
            onMouseMove={isTouchDevice ? undefined : handleMouseMove}
            onMouseUp={isTouchDevice ? undefined : handleMouseUp}
            onMouseLeave={isTouchDevice ? undefined : handleMouseLeave}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            style={{
              cursor: isResizing ? getResizeCursor(resizeHandle) : (isDragging ? 'grabbing' : (selectedElement ? (getResizeHandleAtPosition(mousePosition?.x, mousePosition?.y) ? getResizeCursor(getResizeHandleAtPosition(mousePosition?.x, mousePosition?.y)) : 'grab') : 'default')),
              display: 'block',
              width: isTablet ? 'auto' : '100%', // Better sizing for tablets
              height: isTablet ? 'auto' : '100%', // Better sizing for tablets
              maxWidth: '100%',
              maxHeight: '100%',
              touchAction: isTablet ? 'manipulation' : 'none', // Better touch handling for tablets
              border: isTouchDevice && pendingAddType ? '3px dashed #1976d2' : undefined, // More visible border
              borderRadius: isTablet ? '4px' : undefined, // Rounded corners on tablet
            }}
          />
        </Paper>
        {/* Guest List Panel */}
        <Paper
          variant="outlined"
          sx={{
            width: {
              xs: '100%',
              sm: isTablet ? 200 : 250,
              md: 280,
              lg: 300,
              xl: 320
            },
            ml: { sm: 1, md: 2 },
            p: { xs: 1, sm: isTablet ? 1 : 1.5, md: 2 },
            display: 'flex',
            flexDirection: 'column',
            height: { xs: 'auto', sm: '100%', md: '100%' },
            overflow: 'hidden', // Prevent double scrollbars
            flexShrink: 0,
          }}
        >
          <Typography variant="subtitle2" gutterBottom align="center">
            {t('venue.floorPlanEditor.guestList', 'Guest List')}
          </Typography>
          <TextField
            fullWidth
            size="small"
            placeholder={t('venue.floorPlanEditor.searchGuests', 'Search guests...')}
            value={guestSearchTerm}
            onChange={(e) => setGuestSearchTerm(e.target.value)}
            sx={{ mb: 2 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
          />
          <Typography variant="caption" align="center" sx={{ mb: 1 }}>
            {isTablet
              ? t('venue.floorPlanEditor.tapOrDragGuests', 'Tap or drag guests onto tables or seats')
              : isTouchDevice
                ? t('venue.floorPlanEditor.tapGuestToAssign', 'Tap a guest, then tap a table/seat to assign')
                : t('venue.floorPlanEditor.dragGuestsHere', 'Drag guests onto tables or seats')}
          </Typography>
          <Box sx={{ overflow: 'auto', flexGrow: 1 }}>
            <List dense sx={{ 
              width: '100%', 
              p: 0, 
              display: 'flex', 
              flexDirection: { xs: 'row', sm: isTablet ? 'row' : 'column' },
              flexWrap: { xs: 'nowrap', sm: isTablet ? 'wrap' : 'nowrap' },
              gap: isTablet ? 0.5 : 1,
              overflow: 'auto'
            }}>
              {filteredGuests.length > 0 ? (
                filteredGuests.map((guest) => (
                  <ListItem
                    key={guest._id}
                    disablePadding
                    sx={{
                      mb: { xs: 0, sm: isTablet ? 0.5 : 1 },
                      mr: { xs: 1, sm: isTablet ? 0.5 : 0 },
                      border: '1px solid #e0e0e0',
                      borderRadius: 1,
                      '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' },
                      cursor: isTouchDevice ? 'pointer' : 'grab',
                      minWidth: isTablet ? 90 : 80,
                      minHeight: isTablet ? 60 : 48,
                      width: isTablet ? 'calc(50% - 4px)' : 'auto', // Two columns on tablet
                      background: isTouchDevice && pendingGuest && pendingGuest._id === guest._id ? '#e3f2fd' : undefined,
                      // Add a more obvious selection state for tablets
                      boxShadow: isTouchDevice && pendingGuest && pendingGuest._id === guest._id ? '0 0 0 2px #1976d2' : 'none',
                    }}
                    draggable={!isTouchDevice}
                    onDragStart={isTouchDevice ? undefined : (e) => handleGuestDragStart(e, guest)}
                    onClick={isTouchDevice ? () => setPendingGuest(guest) : undefined}
                  >
                    <ListItemButton dense>
                      <Box sx={{ width: '100%' }}>
                        <Typography variant="body2" fontWeight="bold">
                          {guest.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" noWrap>
                          {guest.email}
                        </Typography>
                        <Box sx={{ mt: 0.5 }}>
                          <Chip
                            label={guest.rsvpStatus}
                            color={
                              guest.rsvpStatus === 'Confirmed' ? 'success' :
                              guest.rsvpStatus === 'Pending' ? 'warning' : 'error'
                            }
                            size="small"
                            sx={{ height: 20, fontSize: '0.7rem' }}
                          />
                        </Box>
                      </Box>
                    </ListItemButton>
                  </ListItem>
                ))
              ) : (
                <Box sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    {t('venue.floorPlanEditor.noGuestsFound', 'No guests found matching your search.')}
                  </Typography>
                </Box>
              )}
            </List>
          </Box>
        </Paper>
      </Box>
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        {selectedElement && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="body2" sx={{ mr: 2 }}>
              {t('venue.floorPlanEditor.selected', 'Selected')}: <strong>{selectedElement.type === 'table' ? t('venue.floorPlanEditor.table', 'Table') : t('venue.floorPlanEditor.seat', 'Seat')} {selectedElement.label || selectedElement.id}</strong>
            </Typography>
            {selectedElement.type === 'seat' && (
              <Button
                variant="outlined"
                size="small"
                onClick={handleOpenGuestDialog}
                startIcon={<EventSeatIcon />}
              >
                {selectedElement.assignedGuests && selectedElement.assignedGuests.length > 0 ? t('venue.floorPlanEditor.changeAssignment', 'Change Assignment') : t('venue.floorPlanEditor.assignGuest', 'Assign Guest')}
              </Button>
            )}
            {selectedElement.type === 'table' && (
              <Button
                variant="outlined"
                size="small"
                onClick={handleOpenGuestDialog}
                startIcon={<TableRestaurantIcon />}
              >
                {selectedElement.assignedGuests && selectedElement.assignedGuests.length > 0 ?
                  `${t('venue.floorPlanEditor.manageGuests', 'Manage Guests')} (${selectedElement.assignedGuests.length})` : t('venue.floorPlanEditor.assignGuests', 'Assign Guests')}
              </Button>
            )}
          </Box>
        )}
        <Button
          className="save-floor-plan-button"
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSave}
        >
          {t('venue.floorPlanEditor.save', 'Save Floor Plan')}
        </Button>
      </Box>
      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedElement && selectedElement.assignedGuests && selectedElement.assignedGuests.length > 0 && (
          <MenuItem onClick={handleRemoveAssignment}>
            <ListItemIcon>
              {selectedElement.type === 'seat' ?
                <EventSeatIcon fontSize="small" /> :
                <TableRestaurantIcon fontSize="small" />}
            </ListItemIcon>
            <ListItemText>
              {selectedElement.type === 'seat' ? t('venue.floorPlanEditor.removeGuestAssignment', 'Remove Guest Assignment') : t('venue.floorPlanEditor.removeAllGuestAssignments', 'Remove All Guest Assignments')}
            </ListItemText>
          </MenuItem>
        )}
        <MenuItem onClick={handleMoveToFront}>
          <ListItemIcon>
            <ArrowUpwardIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('venue.floorPlanEditor.bringToFront', 'Bring to Front')}</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMoveToBack}>
          <ListItemIcon>
            <ArrowDownwardIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('venue.floorPlanEditor.sendToBack', 'Send to Back')}</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleDeleteElement}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('venue.floorPlanEditor.deleteElement', 'Delete Element')}</ListItemText>
        </MenuItem>
      </Menu>
      {/* Guest Assignment Dialog */}
      <Dialog
        open={openGuestDialog}
        onClose={handleCloseGuestDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {isTableAssignment ?
            `${t('venue.floorPlanEditor.assignGuestsToTable', 'Assign Guests to Table')} ${selectedElement?.label || ''}` :
            `${t('venue.floorPlanEditor.assignGuestToSeat', 'Assign Guest to Seat')} ${selectedElement?.label || ''}`}
        </DialogTitle>
        <DialogContent dividers>
          {isTableAssignment ? (
            <GuestAssignmentSelector
              ref={guestSelectorRef}
              guests={guestList}
              onAssign={handleAssignGuest}
              isMultiSelect={true}
              currentAssignments={selectedElement?.assignedGuests || []}
            />
          ) : (
            <GuestAssignmentSelector
              ref={guestSelectorRef}
              guests={guestList}
              onAssign={handleAssignGuest}
              currentAssignment={selectedElement?.assignedGuests?.[0] || null}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseGuestDialog}>{t('common.cancel', 'Cancel')}</Button>
          <Button
            onClick={() => {
              // Call the handleAssign method exposed by the GuestAssignmentSelector
              if (guestSelectorRef.current) {
                guestSelectorRef.current.handleAssign();
              }
            }}
            color="primary"
            variant="contained"
          >
            {t('common.apply', 'Apply')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

const VenueManagement = () => {
  const { t } = useTranslation();
  const { currentEvent } = useContext(EventContext) || { currentEvent: null };
  const { user } = useContext(AuthContext) || { user: null };
  const [venues, setVenues] = useState([]);
  const [guests, setGuests] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingVenue, setEditingVenue] = useState(null);
  const [newVenue, setNewVenue] = useState({
    name: '',
    address: '',
    capacity: 1, // Default to 1 to ensure a valid number
    description: '',
    image: 'https://via.placeholder.com/300x200?text=New+Venue',
    floorPlan: null
  });
  const [selectedVenue, setSelectedVenue] = useState(null);
  const [openFloorPlanDialog, setOpenFloorPlanDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isLargeScreen, setIsLargeScreen] = useState(false);
  const [isUltraWide, setIsUltraWide] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const mobile = width < 600;
      const tablet = width >= 600 && width < 1024;
      const largeScreen = width >= 1440 && width < 1920;
      const ultraWide = width >= 1920;

      setIsMobile(mobile);
      setIsTablet(tablet);
      setIsLargeScreen(largeScreen);
      setIsUltraWide(ultraWide);
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Fetch venues and guests from API when event changes
  useEffect(() => {
    if (!currentEvent) {
      return;
    }

    const fetchData = async () => {
      setIsLoading(true);
      setError('');
      try {
        // Get auth token from localStorage
        const token = localStorage.getItem('token');

        if (!currentEvent?._id) {
          setError('No event selected. Please select an event first.');
          setIsLoading(false);
          return;
        }

        console.log('Fetching venues with token:', !!token);
        console.log('Current event:', currentEvent);

        // Fetch venues
        try {
          if (token) {
            const venuesResponse = await fetch(`/api/resources/venues?eventId=${currentEvent._id}`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            if (venuesResponse.ok) {
              const venuesData = await venuesResponse.json();
              setVenues(venuesData);
            } else {
              console.warn('Failed to fetch venues, using sample data');
              setVenues([
                {
                  _id: '1',
                  name: 'Sample Venue',
                  address: '123 Sample St',
                  capacity: 100,
                  description: 'A sample venue for testing',
                  floorPlan: { width: 1000, height: 800, elements: [], background: null }
                }
              ]);
            }
          } else {
            console.log('No authentication token, using sample venue data');
            setVenues([
              {
                _id: '1',
                name: 'Sample Venue',
                address: '123 Sample St',
                capacity: 100,
                description: 'A sample venue for testing',
                floorPlan: { width: 1000, height: 800, elements: [], background: null }
              }
            ]);
          }
        } catch (venueError) {
          console.error('Error fetching venues:', venueError);
          setVenues([
            {
              _id: '1',
              name: 'Sample Venue',
              address: '123 Sample St',
              capacity: 100,
              description: 'A sample venue for testing',
              floorPlan: { width: 1000, height: 800, elements: [], background: null }
            }
          ]);
        }

        // Select the first venue by default if there are venues and none is currently selected
        if (venues.length > 0 && !selectedVenue) {
          setSelectedVenue(venues[0]);
        }

        // Fetch guests for the same event (needed for seat assignments)
        try {
          if (token) {
            const guestsResponse = await fetch(`/api/resources/guests?eventId=${currentEvent._id}`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            if (guestsResponse.ok) {
              const guestsData = await guestsResponse.json();
              setGuests(guestsData);
            } else {
              console.warn('Failed to fetch guests, using sample data');
              setGuests([
                { _id: '1', name: 'John Doe', email: '<EMAIL>', rsvpStatus: 'Confirmed' },
                { _id: '2', name: 'Jane Smith', email: '<EMAIL>', rsvpStatus: 'Pending' },
                { _id: '3', name: 'Bob Johnson', email: '<EMAIL>', rsvpStatus: 'Confirmed' }
              ]);
            }
          } else {
            console.log('No authentication token, using sample guest data');
            setGuests([
              { _id: '1', name: 'John Doe', email: '<EMAIL>', rsvpStatus: 'Confirmed' },
              { _id: '2', name: 'Jane Smith', email: '<EMAIL>', rsvpStatus: 'Pending' },
              { _id: '3', name: 'Bob Johnson', email: '<EMAIL>', rsvpStatus: 'Confirmed' }
            ]);
          }
        } catch (guestError) {
          console.error('Error fetching guests:', guestError);
          setGuests([
            { _id: '1', name: 'John Doe', email: '<EMAIL>', rsvpStatus: 'Confirmed' },
            { _id: '2', name: 'Jane Smith', email: '<EMAIL>', rsvpStatus: 'Pending' },
            { _id: '3', name: 'Bob Johnson', email: '<EMAIL>', rsvpStatus: 'Confirmed' }
          ]);
        }
      } catch (error) {
        console.error('Error fetching venues and guests:', error);
        setError(`Could not load venues or guests: ${error.message}`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentEvent]);



  const handleOpenDialog = (venue = null) => {
    if (venue) {
      setEditingVenue(venue);
      // Ensure capacity is a valid number
      const venueData = { ...venue };
      venueData.capacity = parseInt(venue.capacity, 10) || 1;
      setNewVenue(venueData);
    } else {
      setEditingVenue(null);
      setNewVenue({
        name: '',
        address: '',
        capacity: 1, // Default to 1 to ensure a valid number
        description: '',
        image: 'https://via.placeholder.com/300x200?text=New+Venue',
        floorPlan: null
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingVenue(null);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewVenue({ ...newVenue, [name]: value });
  };

  const handleSaveVenue = async () => {
    if (!currentEvent) {
      alert(t('guests.errors.selectEventFirst', 'Please select an event first'));
      return;
    }

    // Validate form data
    if (!newVenue.name.trim()) {
      alert(t('errors.required', 'This field is required'));
      return;
    }

    if (!newVenue.address.trim()) {
      alert(t('errors.required', 'This field is required'));
      return;
    }

    // Ensure capacity is a valid number
    const capacity = parseInt(newVenue.capacity, 10);
    if (isNaN(capacity) || capacity < 0) {
      alert(t('venue.form.capacityHelperText', 'Required field - must be a positive number'));
      return;
    }

    try {
      // Get auth token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error(t('errors.authTokenNotFound', 'Authentication token not found. Please log in again.'));
      }

      // Prepare venue data with validated values
      const venueData = {
        ...newVenue,
        capacity: capacity, // Use the parsed integer value
        name: newVenue.name.trim(),
        address: newVenue.address.trim(),
        description: newVenue.description.trim()
      };

      // Use development routes if no authentication
      const useDevRoute = !token || !user;

      if (editingVenue) {
        // Update existing venue
        const endpoint = useDevRoute
          ? `/api/resources/venues/dev-update/${editingVenue._id}`
          : `/api/resources/venues/${editingVenue._id}`;

        const response = await fetch(endpoint, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': useDevRoute ? '' : `Bearer ${token}`
          },
          body: JSON.stringify(venueData),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || t('venue.errors.updateFailed', 'Failed to update venue. Please try again.'));
        }

        const updatedVenue = await response.json();
        setVenues(venues.map(v => v._id === editingVenue._id ? updatedVenue : v));
      } else {
        // Add new venue
        const endpoint = useDevRoute ? '/api/resources/venues/dev-create' : '/api/resources/venues';

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': useDevRoute ? '' : `Bearer ${token}`
          },
          body: JSON.stringify({
            ...venueData,
            eventId: currentEvent._id,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || t('venue.errors.createFailed', 'Failed to create venue. Please try again.'));
        }

        const createdVenue = await response.json();
        setVenues([...venues, createdVenue]);
      }
      handleCloseDialog();
    } catch (error) {
      console.error('Error saving venue:', error);
      alert(t('venue.errors.saveError', 'Error saving venue:') + ' ' + error.message);
    }
  };

  const handleDeleteVenue = async (id) => {
    try {
      // Get auth token from localStorage
      const token = localStorage.getItem('token');
      const useDevRoute = !token || !user;

      const endpoint = useDevRoute
        ? `/api/resources/venues/dev-delete/${id}`
        : `/api/resources/venues/${id}`;

      const response = await fetch(endpoint, {
        method: 'DELETE',
        headers: {
          'Authorization': useDevRoute ? '' : `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || t('venue.errors.deleteFailed', 'Failed to delete venue. Please try again.'));
      }

      setVenues(venues.filter(venue => venue._id !== id));
      if (selectedVenue && selectedVenue._id === id) {
        setSelectedVenue(null);
      }
    } catch (error) {
      console.error('Error deleting venue:', error);
      alert(t('venue.errors.deleteError', 'Error deleting venue:') + ' ' + error.message);
    }
  };

  const handleSelectVenue = (venue) => {
    setSelectedVenue(venue);
  };

  const handleOpenFloorPlanDesigner = () => {
    if (!selectedVenue) {
      alert(t('venue.selectVenueFirst', 'Please select a venue first'));
      return;
    }

    if (!selectedVenue._id) {
      alert(t('venue.invalidVenueSelected', 'Invalid venue selected. Please select a valid venue.'));
      return;
    }

    console.log('Opening floor plan designer for venue:', selectedVenue._id);

    // Force a small delay before opening the dialog to ensure clean state
    setTimeout(() => {
      setOpenFloorPlanDialog(true);
    }, 50);
  };

  const handleCloseFloorPlanDialog = () => {
    setOpenFloorPlanDialog(false);
  };

  const handleSaveFloorPlan = async (floorPlanElements, backgroundImage, width, height) => {
    if (!selectedVenue) {
      alert(t('venue.selectVenueFirst', 'Please select a venue first'));
      return;
    }

    if (!selectedVenue._id) {
      alert(t('venue.invalidVenueSelected', 'Invalid venue selected. Please select a valid venue.'));
      return;
    }

    try {
      // Get auth token from localStorage
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error(t('errors.authTokenNotFound', 'Authentication token not found. Please log in again.'));
      }

      console.log('Original floor plan elements:', JSON.stringify(floorPlanElements));

      // Process elements to ensure they have proper guest assignments
      const processedElements = floorPlanElements.map(element => {
        // Create a new element object to avoid mutation
        let newElement = { ...element };

        // For all elements, ensure they have an assignedGuests array
        // Make sure assignedGuests is an array
        const currentGuests = Array.isArray(newElement.assignedGuests) ? [...newElement.assignedGuests] : [];

        console.log(`Processing element ${newElement.id}, current guests:`, JSON.stringify(currentGuests));

        // If it has assignedGuests, validate them
        if (currentGuests.length > 0) {
          const validGuests = currentGuests.filter(guestId =>
            guests.some(g => g._id === guestId)
          );
          console.log(`Element ${newElement.id} has ${validGuests.length} valid guests:`, JSON.stringify(validGuests));
          newElement.assignedGuests = validGuests;
        } else {
          // If it doesn't have assignedGuests, add an empty array
          newElement.assignedGuests = [];
        }

        // Remove any assignedGuest property (we're only using assignedGuests)
        if ('assignedGuest' in newElement) {
          console.log(`Removing assignedGuest property from element ${newElement.id}`);
          delete newElement.assignedGuest;
        }

        return newElement;
      });

      // Double-check that all elements have the correct properties
      const finalElements = processedElements.map(element => {
        // Create a new element object to avoid mutation
        let finalElement = { ...element };

        // Ensure assignedGuests is an array for all elements
        if (!Array.isArray(finalElement.assignedGuests)) {
          console.warn(`Element ${finalElement.id} still has no assignedGuests array, fixing...`);
          finalElement.assignedGuests = [];
        }

        return finalElement;
      });

      // Log the elements to verify they have the correct properties
      finalElements.forEach(element => {
        console.log(`Element ${element.id} assignedGuests:`, JSON.stringify(element.assignedGuests));
      });

      console.log('Final elements with guest assignments:', JSON.stringify(finalElements));

      // Count elements with guest assignments for debugging
      const seatsWithGuests = finalElements.filter(e => e.type === 'seat' && e.assignedGuests && e.assignedGuests.length > 0).length;
      const tablesWithGuests = finalElements.filter(e => e.type === 'table' && e.assignedGuests && e.assignedGuests.length > 0).length;

      console.log(`Guest assignments: ${seatsWithGuests} seats and ${tablesWithGuests} tables have guests assigned`);

      // Log a sample table with guests if available
      const sampleTable = finalElements.find(e => e.type === 'table' && e.assignedGuests && e.assignedGuests.length > 0);
      if (sampleTable) {
        console.log('Sample table with guests:', JSON.stringify(sampleTable));
      }

      // Create a clean copy of the elements to send to the backend
      const cleanElements = finalElements.map(element => {
        // Create a new object with only the properties we want to send
        const cleanElement = { ...element };

        // For all elements, ensure assignedGuests is an array
        cleanElement.assignedGuests = Array.isArray(cleanElement.assignedGuests) ?
          [...cleanElement.assignedGuests] : [];

        // Remove any assignedGuest property (we're only using assignedGuests)
        if ('assignedGuest' in cleanElement) {
          delete cleanElement.assignedGuest;
        }

        return cleanElement;
      });

      const floorPlanData = {
        width: width || 800,
        height: height || 600,
        elements: cleanElements, // Use the clean elements array
        background: backgroundImage // Now supports background images
      };

      console.log('Saving floor plan for venue ID:', selectedVenue._id);

      // Log the full floor plan data being sent to the backend
      console.log('Floor plan data being sent to backend:', JSON.stringify(floorPlanData, null, 2));

      const response = await fetch(`/api/resources/venues/${selectedVenue._id}/floorplan`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ floorPlan: floorPlanData }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || t('venue.errors.saveFloorPlanFailed', 'Failed to save floor plan. Please try again.'));
      }

      const updatedVenue = await response.json();
      setVenues(venues.map(v => v._id === selectedVenue._id ? updatedVenue : v));
      setSelectedVenue(updatedVenue);
      handleCloseFloorPlanDialog();
      alert(t('common.success', 'Success'));
    } catch (error) {
      console.error('Error saving floor plan:', error);
      alert(t('venue.errors.saveFloorPlanFailed', 'Failed to save floor plan. Please try again.') + ': ' + error.message);
    }
  };

  // Show message when no event is selected
  if (!currentEvent) {
    return (
      <Container maxWidth="lg" sx={{ height: '100%', py: { xs: 0, sm: 1 } }}>
        <Paper sx={{ p: { xs: 1, sm: 2 }, height: { xs: 'auto', md: 'calc(100vh - 80px)' }, display: 'flex', flexDirection: 'column' }}>
          <Typography variant="h4" component="h1" sx={{ mb: 2 }}>
            {t('venue.title', 'Venue Management')}
          </Typography>
          <Alert severity="info">
            {t('venue.noEventSelected', 'Please select an event to manage venues.')}
          </Alert>
        </Paper>
      </Container>
    );
  }

  return (
    <Box sx={{
      height: 'calc(100vh - 140px)', // Account for navbar + container margins + padding
      overflow: 'hidden'
    }}>
      <Paper sx={{
        p: { xs: 1, sm: 2 },
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {/* Error Alert */}
        {error && (
          <Box sx={{ my: 2 }}>
            <Alert severity="error">{error}</Alert>
          </Box>
        )}
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, justifyContent: 'space-between', alignItems: { xs: 'flex-start', sm: 'center' }, mb: 2, gap: { xs: 1, sm: 0 } }}>
          <Typography variant="h4" component="h1">
            {t('venue.title', 'Venue Management')}
          </Typography>
          {currentEvent && (
            <Typography variant="subtitle1" color="text.secondary">
              {t('events.generic', 'Event')}: {currentEvent.title}
            </Typography>
          )}
        </Box>

        {isLoading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <Typography>{t('common.loading', 'Loading...')}</Typography>
          </Box>
        )}

        {/* Responsive layout: stack vertically on small screens */}
        <Box
          className="floor-plan-designer-container"
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            gap: { xs: 1, sm: 2, md: 3 },
            flexGrow: 1,
            overflow: 'hidden',
            height: '100%',
            minHeight: 0, // Allow flex children to shrink
          }}
        >
          {/* Left side - Venues List */}
          <Paper
            variant="outlined"
            sx={{
              width: {
                xs: '100%',
                sm: '100%',
                md: '25%',
                lg: '20%',
                xl: '18%'
              },
              minWidth: { xs: 0, md: 280, lg: 320 },
              maxWidth: { xs: 'none', md: 400 },
              p: { xs: 1, sm: 1.5, md: 2 },
              mb: { xs: 1, md: 0 },
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
              height: { xs: 'auto', md: '100%' },
              maxHeight: { xs: 300, sm: 350, md: 'none' },
            }}
          >
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6">{t('venue.venues', 'Venues')}</Typography>
              <Button
                variant="contained"
                color="primary"
                size="small"
                startIcon={<AddIcon />}
                onClick={() => handleOpenDialog()}
              >
                {t('venue.addVenue', 'Add Venue')}
              </Button>
            </Box>

            {/* Venues List */}
            <Box sx={{ overflow: 'auto', flexGrow: 1 }}>
              {venues.length > 0 ? (
                <List sx={{ width: '100%' }}>
                  {venues.map((venue) => (
                    <ListItem
                      key={venue._id}
                      disablePadding
                      sx={{
                        mb: 1,
                        border: selectedVenue && selectedVenue._id === venue._id ? '1px solid #6200ea' : '1px solid #e0e0e0',
                        borderRadius: 1,
                        bgcolor: selectedVenue && selectedVenue._id === venue._id ? 'rgba(98, 0, 234, 0.05)' : 'transparent'
                      }}
                    >
                      <ListItemButton onClick={() => handleSelectVenue(venue)}>
                        <Box sx={{ width: '100%' }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="subtitle1" fontWeight="bold">
                              {venue.name}
                            </Typography>
                            <Box>
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleOpenDialog(venue);
                                }}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteVenue(venue._id);
                                }}
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Box>
                          </Box>
                          <Typography variant="body2" color="text.secondary" noWrap>
                            {venue.address}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {t('venue.form.capacity', 'Capacity')}: {venue.capacity} people
                          </Typography>
                          {venue.floorPlan && (
                            <Chip
                              size="small"
                              label={t('venue.floorPlan', 'Floor Plan')}
                              color="primary"
                              variant="outlined"
                              sx={{ mt: 1 }}
                            />
                          )}
                        </Box>
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Paper sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body1">
                    {t('venue.noVenues', 'No venues found')}. {t('venue.createFirstVenue', 'Create your first venue to get started.')}
                  </Typography>
                </Paper>
              )}
            </Box>
          </Paper>

          {/* Right side - Venue Details and Floor Plan */}
          <Paper
            variant="outlined"
            sx={{
              width: {
                xs: '100%',
                sm: '100%',
                md: '75%',
                lg: '80%',
                xl: '82%'
              },
              minWidth: 0,
              p: { xs: 1, sm: 1.5, md: 2 },
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
              height: { xs: 'auto', md: '100%' },
              flexGrow: 1,
            }}
          >
            {selectedVenue ? (
              <>
                {/* Venue Details Section */}
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h5">{selectedVenue.name}</Typography>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<EditIcon />}
                      onClick={() => handleOpenDialog(selectedVenue)}
                    >
                      {t('common.edit', 'Edit')}
                    </Button>
                  </Box>

                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">
                        <strong>{t('venue.form.address', 'Address')}:</strong> {selectedVenue.address}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">
                        <strong>{t('venue.form.capacity', 'Capacity')}:</strong> {selectedVenue.capacity} {t('common.people', 'people')}
                      </Typography>
                    </Grid>
                    {selectedVenue.description && (
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary">
                          <strong>{t('venue.form.description', 'Description')}:</strong> {selectedVenue.description}
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                </Box>

                {/* Floor Plan Section */}
                <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6">{t('venue.floorPlan', 'Floor Plan')}</Typography>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={selectedVenue.floorPlan ? <EditIcon /> : <AddIcon />}
                      onClick={handleOpenFloorPlanDesigner}
                    >
                      {selectedVenue.floorPlan ? t('venue.editFloorPlan', 'Edit Floor Plan') : t('venue.createFloorPlan', 'Create Floor Plan')}
                    </Button>
                  </Box>

                  {selectedVenue.floorPlan ? (
                    <Paper
                      variant="outlined"
                      sx={{
                        flexGrow: 1,
                        overflow: 'hidden',
                        position: 'relative',
                        height: 'calc(100% - 80px)',
                        minHeight: { xs: 300, sm: 400, md: 500 },
                        display: 'flex',
                        flexDirection: 'column'
                      }}
                    >
                      <FloorPlanPreview
                        venue={selectedVenue}
                        guestList={guests}
                      />
                    </Paper>
                  ) : (
                    <Paper
                      variant="outlined"
                      sx={{
                        p: { xs: 2, sm: 3 },
                        textAlign: 'center',
                        flexGrow: 1,
                        height: 'calc(100% - 80px)',
                        minHeight: { xs: 300, sm: 400, md: 500 },
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center'
                      }}
                    >
                      <PlaceIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="h6" gutterBottom>
                        {t('venue.noFloorPlanYet', 'No Floor Plan Yet')}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        {t('venue.createFloorPlanDescription', 'Create a floor plan to arrange tables and seats for this venue.')}
                      </Typography>
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={<AddIcon />}
                        onClick={handleOpenFloorPlanDesigner}
                      >
                        {t('venue.createFloorPlan', 'Create Floor Plan')}
                      </Button>
                    </Paper>
                  )}
                </Box>
              </>
            ) : (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                <Typography variant="body1" color="text.secondary">
                  {t('venue.selectVenuePrompt', 'Please select a venue from the list to view details and floor plan.')}
                </Typography>
              </Box>
            )}
          </Paper>
        </Box>
      </Paper>

      {/* Venue Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingVenue ? t('venue.editVenue', 'Edit Venue') : t('venue.addNewVenue', 'Add New Venue')}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={`${t('venue.form.name', 'Name')} *`}
                name="name"
                value={newVenue.name}
                onChange={handleInputChange}
                required
                helperText={t('common.requiredField', 'Required field')}
                size="small"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={`${t('venue.form.address', 'Address')} *`}
                name="address"
                value={newVenue.address}
                onChange={handleInputChange}
                required
                helperText={t('common.requiredField', 'Required field')}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={`${t('venue.form.capacity', 'Capacity')} *`}
                name="capacity"
                type="number"
                value={newVenue.capacity}
                onChange={handleInputChange}
                required
                inputProps={{ min: 0 }}
                helperText={t('venue.form.capacityHelperText', 'Required field - must be a positive number')}
                size="small"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('venue.form.description', 'Description')}
                name="description"
                value={newVenue.description}
                onChange={handleInputChange}
                multiline
                rows={4}
                size="small"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>{t('common.cancel', 'Cancel')}</Button>
          <Button
            onClick={handleSaveVenue}
            variant="contained"
            color="primary"
            disabled={!newVenue.name.trim() || !newVenue.address.trim() || isNaN(parseInt(newVenue.capacity, 10)) || parseInt(newVenue.capacity, 10) < 0}
          >
            {t('common.save', 'Save')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Floor Plan Designer Dialog */}
      <Dialog
        open={openFloorPlanDialog}
        onClose={handleCloseFloorPlanDialog}
        maxWidth={false} // Remove maxWidth constraint
        fullWidth
        fullScreen={isMobile} // Full screen on mobile only
        TransitionProps={{
          onEntered: () => {
            // This callback is fired when the dialog is fully open
            console.log('Floor plan dialog fully opened');
          }
        }}
        PaperProps={{
          sx: {
            m: { xs: 0, sm: 1, md: 2 },
            width: { xs: '100%', sm: '98vw', md: '95vw', lg: '90vw', xl: '85vw' },
            maxWidth: 'none',
            height: { xs: '100%', sm: '95vh', md: '90vh' },
            maxHeight: { xs: '100vh', sm: '95vh', md: '90vh' },
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden' // Prevent double scrollbars
          }
        }}
      >
        <DialogTitle>
          {t('venue.designFloorPlan', 'Design Floor Plan')} - {selectedVenue?.name}
        </DialogTitle>
        <DialogContent dividers sx={{ p: { xs: 1, sm: isTablet ? 2 : 3 }, flexGrow: 1, overflow: 'hidden' }}>
          <FloorPlanDesigner
            venue={selectedVenue}
            onSave={handleSaveFloorPlan}
            guestList={guests}
            isTablet={isTablet}
            isMobile={isMobile}
            isLargeScreen={isLargeScreen}
            isUltraWide={isUltraWide}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseFloorPlanDialog}>{t('common.cancel', 'Cancel')}</Button>
          <Button 
            onClick={() => {
              // Get a reference to the FloorPlanDesigner component's save button
              // and trigger its click function
              const saveButton = document.querySelector('.save-floor-plan-button');
              if (saveButton) {
                saveButton.click();
              } else {
                console.error('Could not find floor plan designer save button');
              }
            }} 
            color="primary" 
            variant="contained"
          >
            {t('common.applyChanges', 'Apply Changes')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VenueManagement;